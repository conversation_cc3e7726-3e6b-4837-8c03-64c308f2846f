# -*- coding: utf-8 -*-
# TMS API Configuration and Settings

import frappe
from typing import Dict, Any

class TMSSettings:
    """Centralized settings for TMS API integration"""
    
    @staticmethod
    def get_settings() -> Dict[str, Any]:
        """Get TMS settings from database or defaults"""
        try:
            # Try to get settings from a custom doctype (you can create this)
            settings = frappe.get_single("TMS Settings") if frappe.db.exists("DocType", "TMS Settings") else None
            
            if settings:
                return {
                    "requests_per_minute": settings.get("requests_per_minute", 30),
                    "requests_per_second": settings.get("requests_per_second", 1),
                    "batch_size": settings.get("batch_size", 10),
                    "max_retries": settings.get("max_retries", 3),
                    "retry_backoff_factor": settings.get("retry_backoff_factor", 2),
                    "connect_timeout": settings.get("connect_timeout", 10),
                    "read_timeout": settings.get("read_timeout", 30),
                    "total_timeout": settings.get("total_timeout", 45),
                    "min_delay": settings.get("min_delay", 1.0),
                    "max_delay": settings.get("max_delay", 5.0),
                    "jitter_factor": settings.get("jitter_factor", 0.3),
                    "enabled": settings.get("enabled", True),
                    "debug_mode": settings.get("debug_mode", False)
                }
        except Exception:
            pass
        
        # Default settings if no custom settings found
        return {
            "requests_per_minute": 30,
            "requests_per_second": 1,
            "batch_size": 10,
            "max_retries": 3,
            "retry_backoff_factor": 2,
            "connect_timeout": 10,
            "read_timeout": 30,
            "total_timeout": 45,
            "min_delay": 1.0,
            "max_delay": 5.0,
            "jitter_factor": 0.3,
            "enabled": True,
            "debug_mode": False
        }
    
    @staticmethod
    def update_settings(**kwargs):
        """Update TMS settings"""
        try:
            if frappe.db.exists("DocType", "TMS Settings"):
                settings = frappe.get_single("TMS Settings")
                for key, value in kwargs.items():
                    if hasattr(settings, key):
                        setattr(settings, key, value)
                settings.save()
                return True
        except Exception as e:
            frappe.log_error(f"Failed to update TMS settings: {str(e)}")
        
        return False
    
    @staticmethod
    def is_enabled() -> bool:
        """Check if TMS integration is enabled"""
        settings = TMSSettings.get_settings()
        return settings.get("enabled", True)


# User agent strings for rotation
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

# HTTP status codes that should trigger a retry
RETRY_STATUS_CODES = [429, 500, 502, 503, 504, 520, 521, 522, 523, 524]

# TMS API endpoints
TMS_ENDPOINTS = {
    "offence_check": "https://tms.tpf.go.tz/api/OffenceCheck",
    "base_url": "https://tms.tpf.go.tz"
}

# Request headers template
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept-Encoding": "gzip, deflate, br",
    "DNT": "1",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Cache-Control": "max-age=0"
}

# Error messages
ERROR_MESSAGES = {
    "invalid_plate": "Please provide a valid number plate (minimum 7 characters)",
    "no_input": "Please provide either number plate or reference",
    "timeout": "Request timed out - server may be busy",
    "rate_limited": "Too many requests - please wait before trying again",
    "server_error": "Server error - please try again later",
    "invalid_response": "Invalid response from server",
    "connection_error": "Unable to connect to TMS server",
    "max_retries": "Maximum retry attempts exceeded"
}

def get_error_message(error_type: str, default: str = "Unknown error") -> str:
    """Get localized error message"""
    return _(ERROR_MESSAGES.get(error_type, default))
