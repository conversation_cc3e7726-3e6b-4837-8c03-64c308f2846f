# -*- coding: utf-8 -*-
# Async TMS Handler for high-performance batch processing

import asyncio
import aiohttp
import random
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging
import frappe
from .tms_config import TMSSettings, USER_AGENTS, DEFAULT_HEADERS, TMS_ENDPOINTS, RETRY_STATUS_CODES

logger = logging.getLogger(__name__)

class AsyncTMSHandler:
    """Asynchronous TMS request handler for high-performance batch processing"""
    
    def __init__(self, max_concurrent=5):
        self.settings = TMSSettings.get_settings()
        self.max_concurrent = max_concurrent
        self.session = None
        self.semaphore = None
        self.request_times = []
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self._create_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self._close_session()
        
    async def _create_session(self):
        """Create aiohttp session with proper configuration"""
        timeout = aiohttp.ClientTimeout(
            total=self.settings["total_timeout"],
            connect=self.settings["connect_timeout"],
            sock_read=self.settings["read_timeout"]
        )
        
        connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers=DEFAULT_HEADERS
        )
        
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
        
    async def _close_session(self):
        """Close the aiohttp session"""
        if self.session:
            await self.session.close()
            
    def _get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(USER_AGENTS)
        
    async def _enforce_rate_limit(self):
        """Enforce rate limiting with async sleep"""
        current_time = time.time()
        
        # Clean old request times
        cutoff_time = current_time - 60
        self.request_times = [t for t in self.request_times if t > cutoff_time]
        
        # Check rate limit
        if len(self.request_times) >= self.settings["requests_per_minute"]:
            sleep_time = 60 - (current_time - self.request_times[0])
            if sleep_time > 0:
                logger.info(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
        
        # Add jitter delay
        delay = random.uniform(
            self.settings["min_delay"], 
            self.settings["max_delay"]
        )
        jitter = delay * random.uniform(-self.settings["jitter_factor"], self.settings["jitter_factor"])
        total_delay = max(0.1, delay + jitter)
        
        await asyncio.sleep(total_delay)
        
        # Track request time
        self.request_times.append(time.time())
        
    async def _make_single_request(self, vehicle_identifier: str, is_reference: bool = False) -> Tuple[bool, Dict]:
        """Make a single async request to TMS API"""
        async with self.semaphore:
            await self._enforce_rate_limit()
            
            headers = DEFAULT_HEADERS.copy()
            headers["User-Agent"] = self._get_random_user_agent()
            
            payload = {"vehicle": vehicle_identifier}
            
            for attempt in range(self.settings["max_retries"] + 1):
                try:
                    async with self.session.post(
                        TMS_ENDPOINTS["offence_check"],
                        json=payload,
                        headers=headers
                    ) as response:
                        
                        if response.status == 200:
                            result = await response.json()
                            return True, result
                        elif response.status in RETRY_STATUS_CODES:
                            if attempt < self.settings["max_retries"]:
                                backoff_time = self.settings["retry_backoff_factor"] ** attempt
                                logger.warning(f"HTTP {response.status} for {vehicle_identifier}, retrying in {backoff_time}s")
                                await asyncio.sleep(backoff_time)
                                continue
                            else:
                                return False, {
                                    "error": f"HTTP {response.status} after {self.settings['max_retries']} retries",
                                    "retry": False
                                }
                        else:
                            return False, {
                                "error": f"HTTP {response.status}",
                                "retry": False
                            }
                            
                except asyncio.TimeoutError:
                    if attempt < self.settings["max_retries"]:
                        logger.warning(f"Timeout for {vehicle_identifier}, retrying...")
                        await asyncio.sleep(2 ** attempt)
                        continue
                    else:
                        return False, {
                            "error": "Request timeout",
                            "retry": True
                        }
                        
                except aiohttp.ClientError as e:
                    if attempt < self.settings["max_retries"]:
                        logger.warning(f"Client error for {vehicle_identifier}: {str(e)}, retrying...")
                        await asyncio.sleep(2 ** attempt)
                        continue
                    else:
                        return False, {
                            "error": f"Client error: {str(e)}",
                            "retry": True
                        }
                        
                except Exception as e:
                    logger.error(f"Unexpected error for {vehicle_identifier}: {str(e)}")
                    return False, {
                        "error": f"Unexpected error: {str(e)}",
                        "retry": False
                    }
            
            return False, {"error": "Max retries exceeded", "retry": False}
    
    async def process_batch(self, vehicles: List[Dict]) -> List[Dict]:
        """Process a batch of vehicles asynchronously"""
        tasks = []
        
        for vehicle in vehicles:
            vehicle_identifier = vehicle.get("number_plate") or vehicle.get("name")
            if vehicle_identifier:
                task = self._make_single_request(vehicle_identifier)
                tasks.append((vehicle, task))
        
        results = []
        
        # Process tasks concurrently
        for vehicle, task in tasks:
            try:
                success, result = await task
                results.append({
                    "vehicle": vehicle,
                    "success": success,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"Task failed for vehicle {vehicle}: {str(e)}")
                results.append({
                    "vehicle": vehicle,
                    "success": False,
                    "result": {"error": f"Task failed: {str(e)}"},
                    "timestamp": datetime.now().isoformat()
                })
        
        return results


async def process_vehicles_async(vehicles: List[Dict], max_concurrent: int = 5) -> List[Dict]:
    """
    Process vehicles asynchronously with proper resource management
    
    Args:
        vehicles: List of vehicle dictionaries
        max_concurrent: Maximum concurrent requests
        
    Returns:
        List of results
    """
    async with AsyncTMSHandler(max_concurrent=max_concurrent) as handler:
        return await handler.process_batch(vehicles)


def run_async_batch(vehicles: List[Dict], max_concurrent: int = 5) -> List[Dict]:
    """
    Synchronous wrapper for async batch processing
    
    Args:
        vehicles: List of vehicle dictionaries
        max_concurrent: Maximum concurrent requests
        
    Returns:
        List of results
    """
    try:
        # Get or create event loop
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # Run the async function
        return loop.run_until_complete(
            process_vehicles_async(vehicles, max_concurrent)
        )
        
    except Exception as e:
        logger.error(f"Error in async batch processing: {str(e)}")
        frappe.log_error(
            title="Async TMS Processing Error",
            message=frappe.get_traceback()
        )
        return []


# Frappe integration functions

@frappe.whitelist()
def process_vehicle_batch_async(vehicles, max_concurrent=5):
    """Process vehicle batch using async handler"""
    if not TMSSettings.is_enabled():
        frappe.throw("TMS integration is disabled")
    
    results = run_async_batch(vehicles, max_concurrent)
    
    processed = 0
    errors = 0
    
    for result in results:
        try:
            if result["success"]:
                # Process the successful result
                vehicle = result["vehicle"]
                api_result = result["result"]
                vehicle_identifier = vehicle.get("number_plate") or vehicle.get("name")
                
                # Update fine records based on API response
                data = api_result.get("pending_transactions", [])
                if not data:  # No pending transactions
                    # Mark existing records as paid
                    existing_records = frappe.get_all(
                        "Vehicle Fine Record",
                        filters={"vehicle": vehicle_identifier, "status": ["!=", "PAID"]},
                        fields=["name"]
                    )
                    
                    for record in existing_records:
                        doc = frappe.get_doc("Vehicle Fine Record", record["name"])
                        doc.status = "PAID"
                        doc.save(ignore_permissions=True)
                
                processed += 1
            else:
                errors += 1
                error_msg = result["result"].get("error", "Unknown error")
                frappe.log_error(
                    title=f"Failed to process vehicle {result['vehicle']}",
                    message=error_msg
                )
                
        except Exception as e:
            errors += 1
            frappe.log_error(
                title=f"Error processing result for vehicle {result.get('vehicle', 'unknown')}",
                message=frappe.get_traceback()
            )
    
    frappe.db.commit()
    logger.info(f"Async batch completed: {processed} processed, {errors} errors")
    
    return {
        "processed": processed,
        "errors": errors,
        "total": len(results)
    }
