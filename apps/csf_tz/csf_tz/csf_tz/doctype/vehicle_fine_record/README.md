# Enhanced Vehicle Fine Record System

This enhanced system provides robust, scalable, and human-like interaction with the Tanzania Traffic Management System (TMS) API for checking vehicle fines.

## Key Features

### 1. **Human-like Request Behavior**
- Randomized delays between requests (1-5 seconds with jitter)
- Rotating user agents to simulate different browsers
- Realistic HTTP headers
- Rate limiting to avoid detection

### 2. **Advanced Rate Limiting**
- Configurable requests per minute/second
- Automatic backoff when rate limits are hit
- Intelligent retry logic with exponential backoff
- Request time tracking and enforcement

### 3. **Scalable Architecture**
- Background job processing for large batches
- Async/await support for high concurrency
- Configurable batch sizes
- Timeout handling to prevent system hangs

### 4. **Robust Error Handling**
- Comprehensive retry logic for transient errors
- Detailed error logging and categorization
- Graceful degradation on failures
- Connection pooling and session management

### 5. **Monitoring and Configuration**
- Real-time job status monitoring
- Configurable settings via API
- Performance statistics
- Test connection functionality

## Usage Examples

### Basic Usage (Single Vehicle)

```python
# Check fine for a single vehicle
from csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record import get_fine

# Using number plate
results = get_fine(number_plate="T123ABC")

# Using reference
results = get_fine(reference="REF123")
```

### Batch Processing (Multiple Vehicles)

```python
# Process multiple vehicles
number_plates = ["T123ABC", "T456DEF", "T789GHI"]
results = get_fine(number_plates=number_plates)

# Process references
references = ["REF123", "REF456", "REF789"]
results = get_fine(references=references)
```

### Large Scale Processing (1000+ Vehicles)

```python
# This automatically uses background jobs to prevent timeouts
from csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record import check_fine_all_vehicles

# Process all vehicles in the system
check_fine_all_vehicles(batch_size=10)  # Optional: specify batch size
```

### Async Processing (High Performance)

```python
from csf_tz.csf_tz.doctype.vehicle_fine_record.async_tms_handler import process_vehicle_batch_async

vehicles = [
    {"name": "VEH-001", "number_plate": "T123ABC"},
    {"name": "VEH-002", "number_plate": "T456DEF"},
    # ... more vehicles
]

# Process with 5 concurrent requests
result = process_vehicle_batch_async(vehicles, max_concurrent=5)
```

## Configuration

### Rate Limiting Settings

```python
from csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record import update_tms_config

# Update configuration
update_tms_config(
    requests_per_minute=20,  # Reduce rate for stricter limits
    batch_size=5,           # Smaller batches
    max_retries=5,          # More retries
    min_delay=2.0,          # Longer delays
    max_delay=8.0
)
```

### Environment-Specific Settings

For **development/testing** (more aggressive):
```python
update_tms_config(
    requests_per_minute=60,
    batch_size=20,
    min_delay=0.5,
    max_delay=2.0
)
```

For **production** (conservative):
```python
update_tms_config(
    requests_per_minute=15,
    batch_size=5,
    min_delay=3.0,
    max_delay=10.0
)
```

## Monitoring

### Check Job Status

```python
from csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record import get_fine_check_status

status = get_fine_check_status()
print(f"Active jobs: {status['total_jobs']}")
```

### Test Connection

```python
from csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record import test_tms_connection

result = test_tms_connection("T123ABC")
if result["success"]:
    print("TMS API is accessible")
else:
    print(f"Connection failed: {result['result']['error']}")
```

### Get Statistics

```python
from csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record import get_vehicle_fine_statistics

stats = get_vehicle_fine_statistics()
print(f"Total vehicles: {stats['total_vehicles']}")
print(f"Fine statistics: {stats['fine_statistics']}")
```

## Best Practices

### 1. **For Large Datasets (1000+ vehicles)**
- Use `check_fine_all_vehicles()` which automatically handles background jobs
- Set conservative batch sizes (5-10 vehicles per batch)
- Monitor job queue to avoid overwhelming the system

### 2. **For Real-time Processing**
- Use single vehicle requests with `get_fine(number_plate="...")`
- Implement client-side caching to avoid duplicate requests
- Handle errors gracefully in your UI

### 3. **For High Performance**
- Use async processing with `process_vehicle_batch_async()`
- Limit concurrent requests (max 5-10)
- Monitor server response times and adjust accordingly

### 4. **Error Handling**
- Always check return values and handle errors
- Implement retry logic in your application layer
- Log errors for debugging and monitoring

## Troubleshooting

### Common Issues

1. **Rate Limiting (HTTP 429)**
   - Reduce `requests_per_minute` setting
   - Increase delays between requests
   - Use smaller batch sizes

2. **Timeouts**
   - Increase timeout settings
   - Use background jobs for large batches
   - Check network connectivity

3. **Server Errors (5xx)**
   - Implement exponential backoff
   - Retry failed requests
   - Check TMS server status

### Performance Tuning

1. **For faster processing:**
   - Increase concurrent requests (but monitor for rate limiting)
   - Use async processing
   - Optimize batch sizes

2. **For reliability:**
   - Decrease request rate
   - Increase retry attempts
   - Use longer delays

## Scheduled Jobs

The system automatically runs scheduled jobs:

- **Every 2 hours**: Check fines for all vehicles
- **Every 6 hours**: Check parking bills (separate system)

To modify the schedule, update `hooks.py`:

```python
scheduler_events = {
    "cron": {
        "0 */4 * * *": [  # Every 4 hours instead of 2
            "csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles",
        ],
    },
}
```

## API Reference

### Main Functions

- `get_fine()` - Process single or batch requests
- `check_fine_all_vehicles()` - Process all vehicles with background jobs
- `process_vehicle_batch_async()` - High-performance async processing
- `test_tms_connection()` - Test API connectivity
- `get_fine_check_status()` - Monitor job status
- `update_tms_config()` - Update configuration
- `get_vehicle_fine_statistics()` - Get system statistics

### Configuration Classes

- `TMSRequestConfig` - Main configuration class
- `TMSSettings` - Database-backed settings
- `AsyncTMSHandler` - Async request handler

For detailed API documentation, see the function docstrings in the source code.
